{"best_threshold": 0.25000000000000006, "threshold_optimization_method": "f1_score_maximization", "feature_names": ["cellvdelta_mean", "cellvdelta_std", "cellvdelta_min", "cellvdelta_max", "cellvdelta_median", "averagecellv_mean", "averagecellv_std", "averagecellv_min", "averagecellv_max", "averagecellv_median", "hcellv_mean", "hcellv_std", "hcellv_min", "hcellv_max", "hcellv_median", "lcellv_mean", "lcellv_std", "lcellv_min", "lcellv_max", "lcellv_median", "systemvolt_mean", "systemvolt_std", "systemvolt_min", "systemvolt_max", "systemvolt_median", "totalcurrenta_mean", "totalcurrenta_std", "totalcurrenta_min", "totalcurrenta_max", "totalcurrenta_median", "soc_mean", "soc_std", "soc_min", "soc_max", "soc_median", "htempc_mean", "htempc_std", "htempc_min", "htempc_max", "htempc_median", "ltempc_mean", "ltempc_std", "ltempc_min", "ltempc_max", "ltempc_median", "tempcdelta_mean", "tempcdelta_std", "tempcdelta_min", "tempcdelta_max", "tempcdelta_median", "averagecelltempc_mean", "averagecelltempc_std", "averagecelltempc_min", "averagecelltempc_max", "averagecelltempc_median", "pcspowerset_mean", "pcspowerset_std", "pcspowerset_min", "pcspowerset_max", "pcspowerset_median", "pcspower_mean", "pcspower_std", "pcspower_min", "pcspower_max", "pcspower_median", "averagecellv__absolute_maximum", "averagecellv__maximum", "systemvolt__maximum", "systemvolt__absolute_maximum", "lcellv__absolute_maximum", "lcellv__maximum", "cellvdelta__minimum", "averagecellv__median", "systemvolt__median", "cellvdelta__standard_deviation", "cellvdelta__variance", "systemvolt__root_mean_square", "systemvolt__sum_values", "systemvolt__mean", "cellvdelta__median", "averagecellv__sum_values", "averagecellv__mean", "averagecellv__root_mean_square", "lcellv__median", "lcellv__sum_values", "lcellv__mean", "lcellv__root_mean_square", "cellvdelta__mean", "cellvdelta__sum_values", "cellvdelta__root_mean_square", "totalcurrenta__root_mean_square", "systemvolt__standard_deviation", "systemvolt__variance", "averagecellv__standard_deviation", "averagecellv__variance", "pcspower__root_mean_square", "lcellv__standard_deviation", "lcellv__variance", "hcellv__median", "pcspower__minimum", "totalcurrenta__minimum", "pcspower__absolute_maximum", "pcspower__median", "totalcurrenta__absolute_maximum", "totalcurrenta__median", "pcspower__standard_deviation", "pcspower__variance", "totalcurrenta__standard_deviation", "totalcurrenta__variance", "totalcurrenta__sum_values", "totalcurrenta__mean", "pcspower__sum_values", "pcspower__mean", "hcellv__sum_values", "hcellv__mean", "hcellv__root_mean_square", "hcellv__absolute_maximum", "hcellv__maximum", "averagecellv__minimum", "systemvolt__minimum", "tempcdelta__minimum", "tempcdelta__absolute_maximum", "tempcdelta__maximum", "soc__standard_deviation", "soc__variance", "tempcdelta__mean", "tempcdelta__sum_values", "tempcdelta__root_mean_square", "tempcdelta__median", "lcellv__minimum", "cellvdelta__maximum", "cellvdelta__absolute_maximum", "averagecelltempc__variance", "averagecelltempc__standard_deviation", "soc__median", "ltempc__minimum", "ltempc__standard_deviation", "ltempc__variance", "tempcdelta__standard_deviation", "tempcdelta__variance", "soc__minimum", "ltempc__sum_values", "ltempc__root_mean_square", "ltempc__mean", "ltempc__median", "hcellv__minimum", "ltempc__maximum", "ltempc__absolute_maximum", "htempc__sum_values", "htempc__mean", "htempc__root_mean_square", "htempc__minimum"], "xgboost_version": "3.0.2"}