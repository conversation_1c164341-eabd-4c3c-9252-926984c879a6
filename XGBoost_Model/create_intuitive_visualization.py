#!/usr/bin/env python3
"""
创建直观测试结果的可视化图表
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import json

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

def load_data():
    """加载数据"""
    print("📁 加载直观测试结果...")
    
    # 加载结果数据
    results_df = pd.read_csv('results/intuitive_test_results.csv')
    
    # 加载汇总统计
    with open('results/intuitive_test_summary.json', 'r') as f:
        summary = json.load(f)
    
    print(f"   数据点总数: {len(results_df):,}")
    print(f"   预测正确: {summary['correct_predictions']:,}")
    print(f"   准确率: {summary['accuracy']:.1%}")
    
    return results_df, summary

def create_confusion_matrix_plot(summary):
    """创建混淆矩阵图"""
    print("\n📊 创建混淆矩阵图...")
    
    # 混淆矩阵数据
    confusion_matrix = np.array([
        [summary['true_negatives'], summary['false_positives']],
        [summary['false_negatives'], summary['true_positives']]
    ])
    
    fig, ax = plt.subplots(1, 1, figsize=(8, 6))
    
    # 创建热力图
    sns.heatmap(confusion_matrix, 
                annot=True, 
                fmt='d', 
                cmap='Blues',
                xticklabels=['预测正常', '预测异常'],
                yticklabels=['实际正常', '实际异常'],
                ax=ax,
                cbar_kws={'label': '数据点数量'})
    
    ax.set_title('混淆矩阵 - 直观测试结果', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('预测标签', fontsize=12)
    ax.set_ylabel('真实标签', fontsize=12)
    
    # 添加性能指标文本
    metrics_text = f"""性能指标:
准确率: {summary['accuracy']:.1%}
精确度: {summary['precision']:.1%}
召回率: {summary['recall']:.1%}
F1-Score: {summary['f1_score']:.3f}"""
    
    ax.text(1.05, 0.5, metrics_text, transform=ax.transAxes, 
            fontsize=11, verticalalignment='center',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('results/intuitive_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_time_series_plot(results_df):
    """创建时间序列预测结果图"""
    print("\n📈 创建时间序列预测结果图...")
    
    # 转换时间戳
    results_df['ts'] = pd.to_datetime(results_df['ts'])
    
    # 选择一个时间段进行可视化（前1000个数据点）
    sample_df = results_df.head(1000).copy()
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 上图：真实标签 vs 预测标签
    ax1.plot(sample_df['ts'], sample_df['true_label'], 'o-', 
             color='blue', alpha=0.7, markersize=3, label='真实标签')
    ax1.plot(sample_df['ts'], sample_df['predicted_label'], 's-', 
             color='red', alpha=0.7, markersize=3, label='预测标签')
    
    ax1.set_title('时间序列预测结果对比 (前1000个数据点)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('异常标签 (0=正常, 1=异常)', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 下图：预测概率
    ax2.plot(sample_df['ts'], sample_df['predicted_probability'], 
             color='green', alpha=0.8, linewidth=1.5, label='预测概率')
    ax2.axhline(y=0.46, color='red', linestyle='--', alpha=0.8, label='阈值 (0.46)')
    
    # 标记错误预测
    errors = sample_df[~sample_df['prediction_correct']]
    if len(errors) > 0:
        ax2.scatter(errors['ts'], errors['predicted_probability'], 
                   color='red', s=30, alpha=0.8, label=f'错误预测 ({len(errors)}个)')
    
    ax2.set_title('预测概率时间序列', fontsize=14, fontweight='bold')
    ax2.set_xlabel('时间', fontsize=12)
    ax2.set_ylabel('预测概率', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/intuitive_time_series.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_error_analysis_plot(results_df):
    """创建错误分析图"""
    print("\n🔍 创建错误分析图...")
    
    # 分析错误类型
    fp_data = results_df[results_df['prediction_status'] == 'FP']
    fn_data = results_df[results_df['prediction_status'] == 'FN']
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 错误类型分布
    error_counts = results_df['prediction_status'].value_counts()
    colors = {'TP': 'green', 'TN': 'blue', 'FP': 'orange', 'FN': 'red'}
    bars = ax1.bar(error_counts.index, error_counts.values, 
                   color=[colors[x] for x in error_counts.index])
    ax1.set_title('预测结果分布', fontsize=14, fontweight='bold')
    ax1.set_ylabel('数据点数量', fontsize=12)
    
    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 10,
                f'{int(height)}', ha='center', va='bottom')
    
    # 2. 误报(FP)的概率分布
    if len(fp_data) > 0:
        ax2.hist(fp_data['predicted_probability'], bins=20, alpha=0.7, color='orange')
        ax2.axvline(x=0.46, color='red', linestyle='--', label='阈值 (0.46)')
        ax2.set_title(f'误报(FP)的预测概率分布 (n={len(fp_data)})', fontsize=14, fontweight='bold')
        ax2.set_xlabel('预测概率', fontsize=12)
        ax2.set_ylabel('频次', fontsize=12)
        ax2.legend()
    else:
        ax2.text(0.5, 0.5, '无误报数据', ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('误报(FP)的预测概率分布', fontsize=14, fontweight='bold')
    
    # 3. 漏检(FN)的概率分布
    if len(fn_data) > 0:
        ax3.hist(fn_data['predicted_probability'], bins=20, alpha=0.7, color='red')
        ax3.axvline(x=0.46, color='red', linestyle='--', label='阈值 (0.46)')
        ax3.set_title(f'漏检(FN)的预测概率分布 (n={len(fn_data)})', fontsize=14, fontweight='bold')
        ax3.set_xlabel('预测概率', fontsize=12)
        ax3.set_ylabel('频次', fontsize=12)
        ax3.legend()
    else:
        ax3.text(0.5, 0.5, '无漏检数据', ha='center', va='center', transform=ax3.transAxes)
        ax3.set_title('漏检(FN)的预测概率分布', fontsize=14, fontweight='bold')
    
    # 4. 预测概率整体分布
    ax4.hist(results_df['predicted_probability'], bins=50, alpha=0.7, color='skyblue')
    ax4.axvline(x=0.46, color='red', linestyle='--', linewidth=2, label='阈值 (0.46)')
    ax4.set_title('所有预测概率分布', fontsize=14, fontweight='bold')
    ax4.set_xlabel('预测概率', fontsize=12)
    ax4.set_ylabel('频次', fontsize=12)
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig('results/intuitive_error_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_summary_table(summary):
    """创建汇总表格图"""
    print("\n📋 创建汇总表格...")
    
    fig, ax = plt.subplots(figsize=(10, 8))
    ax.axis('tight')
    ax.axis('off')
    
    # 准备表格数据
    table_data = [
        ['指标', '数值', '说明'],
        ['总数据点', f"{summary['total_data_points']:,}", '测试数据总量'],
        ['预测正确', f"{summary['correct_predictions']:,}", '正确预测的数据点'],
        ['准确率', f"{summary['accuracy']:.1%}", '整体预测准确度'],
        ['', '', ''],
        ['真正例 (TP)', f"{summary['true_positives']:,}", '正确识别的异常'],
        ['真负例 (TN)', f"{summary['true_negatives']:,}", '正确识别的正常'],
        ['误报 (FP)', f"{summary['false_positives']:,}", '错误识别为异常'],
        ['漏检 (FN)', f"{summary['false_negatives']:,}", '错误识别为正常'],
        ['', '', ''],
        ['精确度', f"{summary['precision']:.1%}", 'TP/(TP+FP)'],
        ['召回率', f"{summary['recall']:.1%}", 'TP/(TP+FN)'],
        ['F1-Score', f"{summary['f1_score']:.3f}", '精确度和召回率的调和平均'],
        ['', '', ''],
        ['真实异常率', f"{summary['anomaly_rate_true']:.1%}", '实际异常数据比例'],
        ['预测异常率', f"{summary['anomaly_rate_predicted']:.1%}", '预测异常数据比例']
    ]
    
    # 创建表格
    table = ax.table(cellText=table_data[1:], 
                    colLabels=table_data[0],
                    cellLoc='center',
                    loc='center',
                    colWidths=[0.3, 0.3, 0.4])
    
    # 设置表格样式
    table.auto_set_font_size(False)
    table.set_fontsize(11)
    table.scale(1, 2)
    
    # 设置标题行样式
    for i in range(3):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    # 设置分隔行样式
    for row in [4, 9, 13]:
        for col in range(3):
            table[(row, col)].set_facecolor('#f0f0f0')
    
    # 设置重要指标行样式
    important_rows = [3, 11, 12]  # 准确率、召回率、F1-Score
    for row in important_rows:
        for col in range(3):
            table[(row, col)].set_facecolor('#E3F2FD')
    
    ax.set_title('XGBoost异常检测模型 - 直观测试结果汇总', 
                fontsize=16, fontweight='bold', pad=20)
    
    plt.savefig('results/intuitive_summary_table.png', dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    print("🎨 创建直观测试结果可视化...")
    print("=" * 60)
    
    # 1. 加载数据
    results_df, summary = load_data()
    
    # 2. 创建各种图表
    create_confusion_matrix_plot(summary)
    create_time_series_plot(results_df)
    create_error_analysis_plot(results_df)
    create_summary_table(summary)
    
    print(f"\n✅ 可视化图表创建完成！")
    print(f"📁 保存位置:")
    print(f"   - results/intuitive_confusion_matrix.png")
    print(f"   - results/intuitive_time_series.png") 
    print(f"   - results/intuitive_error_analysis.png")
    print(f"   - results/intuitive_summary_table.png")
    
    print(f"\n🎯 关键发现:")
    print(f"   - 总准确率: {summary['accuracy']:.1%}")
    print(f"   - F1-Score: {summary['f1_score']:.3f}")
    print(f"   - 误报率: {summary['false_positives']/(summary['false_positives']+summary['true_negatives']):.1%}")
    print(f"   - 漏检率: {summary['false_negatives']/(summary['false_negatives']+summary['true_positives']):.1%}")

if __name__ == "__main__":
    main()
