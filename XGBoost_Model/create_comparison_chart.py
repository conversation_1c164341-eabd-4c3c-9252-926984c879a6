#!/usr/bin/env python3
"""
创建测试结果对比图表
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_performance_comparison():
    """创建性能对比图表"""
    
    # 数据
    methods = ['标准测试\n(4_test_model.py)', '改进测试\n(improve_f1_test.py)']
    f1_scores = [0.679, 0.973]
    precisions = [0.514, 0.970]
    recalls = [1.000, 0.977]
    auc_scores = [0.997, 0.997]
    
    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('测试方法性能对比分析', fontsize=16, fontweight='bold')
    
    # 颜色设置
    colors = ['#ff7f7f', '#7fbf7f']
    
    # 1. F1-Score对比
    bars1 = ax1.bar(methods, f1_scores, color=colors, alpha=0.8)
    ax1.set_title('F1-Score 对比', fontweight='bold')
    ax1.set_ylabel('F1-Score')
    ax1.set_ylim(0, 1.1)
    for i, v in enumerate(f1_scores):
        ax1.text(i, v + 0.02, f'{v:.3f}', ha='center', fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 2. Precision对比
    bars2 = ax2.bar(methods, precisions, color=colors, alpha=0.8)
    ax2.set_title('Precision 对比', fontweight='bold')
    ax2.set_ylabel('Precision')
    ax2.set_ylim(0, 1.1)
    for i, v in enumerate(precisions):
        ax2.text(i, v + 0.02, f'{v:.3f}', ha='center', fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 3. Recall对比
    bars3 = ax3.bar(methods, recalls, color=colors, alpha=0.8)
    ax3.set_title('Recall 对比', fontweight='bold')
    ax3.set_ylabel('Recall')
    ax3.set_ylim(0, 1.1)
    for i, v in enumerate(recalls):
        ax3.text(i, v + 0.02, f'{v:.3f}', ha='center', fontweight='bold')
    ax3.grid(True, alpha=0.3)
    
    # 4. AUC-ROC对比
    bars4 = ax4.bar(methods, auc_scores, color=colors, alpha=0.8)
    ax4.set_title('AUC-ROC 对比', fontweight='bold')
    ax4.set_ylabel('AUC-ROC')
    ax4.set_ylim(0.99, 1.001)
    for i, v in enumerate(auc_scores):
        ax4.text(i, v + 0.0002, f'{v:.3f}', ha='center', fontweight='bold')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results/performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_radar_chart():
    """创建雷达图对比"""
    
    # 数据
    categories = ['F1-Score', 'Precision', 'Recall', 'AUC-ROC']
    standard_test = [0.679, 0.514, 1.000, 0.997]
    improved_test = [0.973, 0.970, 0.977, 0.997]
    
    # 角度
    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合
    
    # 数据闭合
    standard_test += standard_test[:1]
    improved_test += improved_test[:1]
    
    # 创建雷达图
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # 绘制
    ax.plot(angles, standard_test, 'o-', linewidth=2, label='标准测试', color='#ff7f7f')
    ax.fill(angles, standard_test, alpha=0.25, color='#ff7f7f')
    
    ax.plot(angles, improved_test, 'o-', linewidth=2, label='改进测试', color='#7fbf7f')
    ax.fill(angles, improved_test, alpha=0.25, color='#7fbf7f')
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(categories)
    ax.set_ylim(0, 1)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
    ax.grid(True)
    
    # 标题和图例
    plt.title('测试方法性能雷达图对比', size=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    
    plt.tight_layout()
    plt.savefig('results/radar_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_improvement_chart():
    """创建改进幅度图表"""
    
    metrics = ['F1-Score', 'Precision', 'Recall', 'AUC-ROC']
    improvements = [
        (0.973 - 0.679) / 0.679 * 100,  # F1改进
        (0.970 - 0.514) / 0.514 * 100,  # Precision改进
        (0.977 - 1.000) / 1.000 * 100,  # Recall改进
        (0.997 - 0.997) / 0.997 * 100   # AUC改进
    ]
    
    # 颜色：正值绿色，负值红色
    colors = ['green' if x > 0 else 'red' for x in improvements]
    
    fig, ax = plt.subplots(figsize=(12, 8))
    bars = ax.bar(metrics, improvements, color=colors, alpha=0.7)
    
    ax.set_title('改进测试相对于标准测试的性能提升', fontsize=16, fontweight='bold')
    ax.set_ylabel('改进幅度 (%)')
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(improvements):
        ax.text(i, v + (5 if v > 0 else -8), f'{v:.1f}%', 
                ha='center', fontweight='bold', 
                color='green' if v > 0 else 'red')
    
    plt.tight_layout()
    plt.savefig('results/improvement_chart.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_summary_table():
    """创建汇总表格"""
    
    data = {
        '测试方法': ['标准测试 (4_test_model.py)', '改进测试 (improve_f1_test.py)', '改进幅度'],
        'F1-Score': ['0.679', '0.973', '+43.3%'],
        'Precision': ['0.514', '0.970', '+88.7%'],
        'Recall': ['1.000', '0.977', '-2.3%'],
        'AUC-ROC': ['0.997', '0.997', '0.0%'],
        '窗口数量': ['1,875', '3,931', '+109.7%'],
        '阈值': ['0.25', '0.46', '+84.0%']
    }
    
    df = pd.DataFrame(data)
    
    # 创建表格图
    fig, ax = plt.subplots(figsize=(14, 6))
    ax.axis('tight')
    ax.axis('off')
    
    table = ax.table(cellText=df.values, colLabels=df.columns,
                    cellLoc='center', loc='center')
    
    # 设置表格样式
    table.auto_set_font_size(False)
    table.set_fontsize(12)
    table.scale(1.2, 1.5)
    
    # 设置标题行样式
    for i in range(len(df.columns)):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    # 设置改进行样式
    for i in range(len(df.columns)):
        table[(3, i)].set_facecolor('#E8F5E8')
        table[(3, i)].set_text_props(weight='bold')
    
    plt.title('测试结果详细对比表', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    plt.savefig('results/summary_table.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("🎨 创建测试结果对比图表...")
    
    # 创建各种图表
    create_performance_comparison()
    print("   ✅ 性能对比图表已保存: results/performance_comparison.png")
    
    create_radar_chart()
    print("   ✅ 雷达图已保存: results/radar_comparison.png")
    
    create_improvement_chart()
    print("   ✅ 改进幅度图表已保存: results/improvement_chart.png")
    
    create_summary_table()
    print("   ✅ 汇总表格已保存: results/summary_table.png")
    
    print("\n🎉 所有图表创建完成！")

if __name__ == "__main__":
    main()
