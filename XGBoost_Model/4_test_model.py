#!/usr/bin/env python3
"""
在真实测试数据上评估模型性能
"""

import pandas as pd
import numpy as np
import pickle
import json
from datetime import datetime
from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score, roc_auc_score
import matplotlib.pyplot as plt
import seaborn as sns

def load_model_and_config():
    """加载训练好的模型和配置"""
    print("📁 加载模型和配置...")
    
    # 加载模型
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    # 加载阈值配置
    with open('models/threshold_config.json', 'r') as f:
        threshold_config = json.load(f)
    
    print(f"   模型加载成功")
    print(f"   最佳阈值: {threshold_config['best_threshold']}")
    
    return model, threshold_config

def preprocess_test_data():
    """预处理测试数据"""
    print("\n🔄 预处理测试数据...")
    
    # 加载原始测试数据
    test_data = pd.read_csv('data/processed_data/test_raw_data.csv')
    print(f"   原始测试数据: {test_data.shape}")
    
    # 检查数据质量
    print(f"   时间范围: {test_data['ts'].min()} 到 {test_data['ts'].max()}")
    print(f"   数据点数: {len(test_data)}")
    
    # 检查是否有异常标签
    if 'is_string_imbalance' in test_data.columns:
        anomaly_count = test_data['is_string_imbalance'].sum()
        print(f"   异常数据点: {anomaly_count}/{len(test_data)} ({anomaly_count/len(test_data)*100:.1f}%)")
    else:
        print("   ⚠️ 测试数据中没有异常标签，将进行无监督预测")
    
    # 转换时间戳
    test_data['ts'] = pd.to_datetime(test_data['ts'])
    test_data = test_data.sort_values('ts').reset_index(drop=True)
    
    return test_data

def create_time_windows(test_data, window_size_seconds=30):
    """创建时间窗口（与训练时保持一致）"""
    print(f"\n🪟 创建时间窗口 (窗口大小: {window_size_seconds}秒)...")
    
    # 计算时间差
    test_data['time_diff'] = test_data['ts'].diff().dt.total_seconds()
    
    # 创建窗口ID
    window_id = 1
    window_ids = [window_id]
    current_window_start = test_data['ts'].iloc[0]
    
    for i in range(1, len(test_data)):
        time_since_window_start = (test_data['ts'].iloc[i] - current_window_start).total_seconds()
        
        if time_since_window_start >= window_size_seconds:
            window_id += 1
            current_window_start = test_data['ts'].iloc[i]
        
        window_ids.append(window_id)
    
    test_data['window_id'] = window_ids
    
    print(f"   创建了 {test_data['window_id'].nunique()} 个窗口")
    print(f"   每个窗口平均数据点数: {len(test_data) / test_data['window_id'].nunique():.1f}")
    
    return test_data

def extract_features(test_data):
    """提取特征（与训练时保持一致）"""
    print("\n🔧 提取特征...")
    
    # 定义核心特征（与训练脚本保持一致）
    CORE_FEATURES = [
        'cellvdelta',        # 电芯电压差（最重要）
        'averagecellv',      # 平均电芯电压
        'hcellv',           # 最高电芯电压
        'lcellv',           # 最低电芯电压
        'systemvolt',       # 系统电压
        'totalcurrenta',    # 总电流
        'soc',              # SOC
        'htempc',           # 最高温度
        'ltempc',           # 最低温度
        'tempcdelta',       # 新增：温度差值
        'averagecelltempc', # 新增：平均电芯温度
        'pcspowerset',      # 新增：PCS功率设定值
        'pcspower'          # 新增：PCS实际功率
    ]
    
    # 检查特征是否存在
    available_features = [f for f in CORE_FEATURES if f in test_data.columns]
    missing_features = [f for f in CORE_FEATURES if f not in test_data.columns]
    
    print(f"   可用特征: {len(available_features)}/{len(CORE_FEATURES)}")
    if missing_features:
        print(f"   缺失特征: {missing_features}")
    
    # 按窗口聚合特征
    print("   聚合原始特征...")
    test_agg_features = test_data.groupby('window_id')[available_features].agg([
        'mean', 'std', 'min', 'max', 'median'
    ]).round(6)
    
    # 展平多级列名
    test_agg_features.columns = ['_'.join(col).strip() for col in test_agg_features.columns.values]
    test_agg_features.reset_index(drop=True, inplace=True)
    
    print(f"   聚合特征形状: {test_agg_features.shape}")
    
    # 创建标签（如果存在异常标签）
    test_labels = None
    if 'is_string_imbalance' in test_data.columns:
        # 按窗口聚合标签（如果窗口中有任何异常，则标记为异常）
        test_labels = test_data.groupby('window_id')['is_string_imbalance'].max().reset_index()
        test_labels['label'] = test_labels['is_string_imbalance'].astype(int)
        print(f"   标签分布: {test_labels['label'].value_counts().to_dict()}")
    
    return test_agg_features, test_labels

def create_tsfresh_features(test_agg_features):
    """创建模拟的TSFresh特征（与训练时保持一致）"""
    print("\n⚙️ 创建TSFresh风格特征...")

    # 从特征重要性文件中获取TSFresh特征名称
    try:
        feature_importance = pd.read_csv('results/feature_importance.csv')
        tsfresh_feature_names = [f for f in feature_importance['feature'] if '__' in f]
        print(f"   从特征重要性文件中找到 {len(tsfresh_feature_names)} 个TSFresh特征")
    except:
        print("   无法加载特征重要性文件，使用默认TSFresh特征")
        tsfresh_feature_names = []

    # 创建TSFresh特征DataFrame
    n_windows = len(test_agg_features)
    tsfresh_features = pd.DataFrame()

    if tsfresh_feature_names:
        # 使用训练时的特征名称，填充模拟值
        for feature_name in tsfresh_feature_names:
            # 从特征名称中提取基础特征名
            if '__' in feature_name:
                base_feature = feature_name.split('__')[0]

                # 检查基础特征是否在聚合特征中
                base_mean_col = f'{base_feature}_mean'
                base_max_col = f'{base_feature}_max'
                base_min_col = f'{base_feature}_min'
                base_std_col = f'{base_feature}_std'
                base_median_col = f'{base_feature}_median'

                if base_mean_col in test_agg_features.columns:
                    # 根据聚合特征创建对应的TSFresh特征
                    if '__maximum' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_max_col]
                    elif '__minimum' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_min_col]
                    elif '__mean' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_mean_col]
                    elif '__standard_deviation' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_std_col].fillna(0)
                    elif '__median' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_median_col]
                    elif '__variance' in feature_name:
                        tsfresh_features[feature_name] = (test_agg_features[base_std_col] ** 2).fillna(0)
                    elif '__absolute_maximum' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_max_col].abs()
                    elif '__root_mean_square' in feature_name:
                        tsfresh_features[feature_name] = np.sqrt((test_agg_features[base_mean_col] ** 2).fillna(0))
                    elif '__sum_values' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_mean_col] * 30  # 假设窗口大小为30
                    else:
                        # 对于其他TSFresh特征，使用均值作为默认值
                        tsfresh_features[feature_name] = test_agg_features[base_mean_col].fillna(0)
                else:
                    # 如果基础特征不可用，填充0
                    tsfresh_features[feature_name] = np.zeros(n_windows)

    # 填充缺失值
    tsfresh_features = tsfresh_features.fillna(0)

    print(f"   TSFresh特征形状: {tsfresh_features.shape}")

    return tsfresh_features

def predict_on_test_data(model, test_features, threshold):
    """在测试数据上进行预测"""
    print(f"\n🔮 模型预测 (阈值: {threshold})...")

    # 获取模型期望的特征名称
    expected_features = model.get_booster().feature_names
    print(f"   模型期望特征数: {len(expected_features)}")

    # 检查特征匹配情况
    missing_features = []
    available_features = []

    for feature in expected_features:
        if feature in test_features.columns:
            available_features.append(feature)
        else:
            missing_features.append(feature)

    if missing_features:
        print(f"   警告: 缺失 {len(missing_features)} 个特征，用0填充")
        print(f"   缺失特征示例: {missing_features[:5]}")

        # 为缺失的特征填充0
        for feature in missing_features:
            test_features[feature] = 0

    # 按照模型期望的顺序重新排列特征
    test_features_ordered = test_features[expected_features]
    print(f"   重新排序后特征形状: {test_features_ordered.shape}")

    # 获取预测概率
    y_pred_proba = model.predict_proba(test_features_ordered)[:, 1]

    # 应用阈值
    y_pred = (y_pred_proba >= threshold).astype(int)

    print(f"   预测概率范围: {y_pred_proba.min():.4f} - {y_pred_proba.max():.4f}")
    print(f"   预测结果分布:")
    print(f"     正常: {(y_pred == 0).sum()}")
    print(f"     异常: {(y_pred == 1).sum()}")
    print(f"     异常率: {y_pred.mean()*100:.1f}%")

    return y_pred_proba, y_pred

def evaluate_predictions(y_true, y_pred, y_pred_proba):
    """评估预测结果"""
    print("\n📊 评估预测结果...")
    
    # 计算性能指标
    f1 = f1_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred, zero_division=0)
    recall = recall_score(y_true, y_pred, zero_division=0)
    auc = roc_auc_score(y_true, y_pred_proba)
    
    print(f"   F1-Score: {f1:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   AUC-ROC: {auc:.3f}")
    
    # 混淆矩阵
    cm = confusion_matrix(y_true, y_pred)
    print(f"\n   混淆矩阵:")
    print(f"   TN: {cm[0,0]:2d} | FP: {cm[0,1]:2d}")
    print(f"   FN: {cm[1,0]:2d} | TP: {cm[1,1]:2d}")
    
    # 详细分类报告
    print(f"\n   详细分类报告:")
    print(classification_report(y_true, y_pred, target_names=['正常', '异常']))
    
    return {
        'f1_score': f1,
        'precision': precision,
        'recall': recall,
        'auc_roc': auc,
        'confusion_matrix': cm.tolist()
    }

def save_test_results(results, y_pred_proba, y_pred, test_labels=None):
    """保存测试结果"""
    print("\n💾 保存测试结果...")
    
    # 保存性能指标
    results['test_date'] = datetime.now().isoformat()
    results['test_samples'] = len(y_pred)
    
    with open('results/test_performance.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # 保存预测结果
    predictions_df = pd.DataFrame({
        'window_id': range(1, len(y_pred) + 1),
        'predicted_probability': y_pred_proba,
        'predicted_label': y_pred
    })
    
    if test_labels is not None:
        predictions_df['true_label'] = test_labels['label'].values
    
    predictions_df.to_csv('results/test_predictions.csv', index=False)
    
    print(f"   测试结果已保存:")
    print(f"     性能指标: results/test_performance.json")
    print(f"     预测结果: results/test_predictions.csv")

def main():
    print("🧪 开始测试模型性能...")
    print("=" * 60)
    
    # 1. 加载模型和配置
    model, threshold_config = load_model_and_config()
    
    # 2. 预处理测试数据
    test_data = preprocess_test_data()
    
    # 3. 创建时间窗口
    test_data = create_time_windows(test_data)
    
    # 4. 提取特征
    test_agg_features, test_labels = extract_features(test_data)
    
    # 5. 创建TSFresh特征
    test_tsfresh_features = create_tsfresh_features(test_agg_features)
    
    # 6. 合并特征
    test_features = pd.concat([test_agg_features, test_tsfresh_features], axis=1)
    print(f"\n🔗 合并后特征形状: {test_features.shape}")
    
    # 7. 进行预测
    y_pred_proba, y_pred = predict_on_test_data(
        model, test_features, threshold_config['best_threshold']
    )
    
    # 8. 评估结果（如果有标签）
    if test_labels is not None:
        results = evaluate_predictions(test_labels['label'].values, y_pred, y_pred_proba)
        
        # 9. 保存结果
        save_test_results(results, y_pred_proba, y_pred, test_labels)
        
        print(f"\n🎯 测试完成！模型在真实测试数据上的表现:")
        print(f"   F1-Score: {results['f1_score']:.3f}")
        print(f"   Precision: {results['precision']:.3f}")
        print(f"   Recall: {results['recall']:.3f}")
        print(f"   AUC-ROC: {results['auc_roc']:.3f}")
        
    else:
        print(f"\n🔮 预测完成！由于没有真实标签，无法计算性能指标")
        print(f"   预测了 {len(y_pred)} 个窗口")
        print(f"   异常窗口数: {y_pred.sum()}")
        
        # 保存预测结果
        save_test_results({}, y_pred_proba, y_pred)
    
    print("\n✅ 测试流程完成！")

if __name__ == "__main__":
    main()
