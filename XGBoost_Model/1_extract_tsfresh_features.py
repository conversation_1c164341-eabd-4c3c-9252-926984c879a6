#!/usr/bin/env python3
"""
1_extract_tsfresh_features.py

从窗口数据中提取tsfresh特征，用于电芯不平衡异常检测模型训练

输入：data/processed_data/train_windows.csv + val_windows.csv
输出：data/processed_data/train_tsfresh_features.csv + val_tsfresh_features.csv
     data/processed_data/feature_metadata.json
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime
from tqdm import tqdm

# tsfresh相关导入
from tsfresh import extract_features
from tsfresh.utilities.dataframe_functions import impute
from tsfresh.feature_extraction import MinimalFCParameters, ComprehensiveFCParameters
from tsfresh.feature_selection import select_features

# ========================================
# 🎯 核心特征配置（用户可修改）
# ========================================
CORE_FEATURES = [
    'cellvdelta',        # 电芯电压差（最重要）
    'averagecellv',      # 平均电芯电压
    'hcellv',           # 最高电芯电压
    'lcellv',           # 最低电芯电压
    'systemvolt',       # 系统电压
    'totalcurrenta',    # 总电流
    'soc',              # SOC
    'htempc',           # 最高温度
    'ltempc',           # 最低温度
    'tempcdelta',
    'averagecelltempc',
    'pcspowerset',
    'pcspower'
]

# 如果需要添加更多特征列，请在上面的列表中添加
# 例如：'hcellstring', 'lcellstring', 'tempcdelta' 等
# ========================================

def setup_directories():
    """创建必要的文件夹"""
    directories = [
        'data/processed_data',
        'models',
        'logs',
        'results',
        'config'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 确保文件夹存在: {directory}")

def load_window_data():
    """加载窗口数据"""
    print("📖 加载窗口数据...")
    
    # 尝试加载训练和验证数据
    train_path = "data/processed_data/train_windows.csv"
    val_path = "data/processed_data/val_windows.csv"
    
    if not os.path.exists(train_path):
        raise FileNotFoundError(f"找不到训练数据文件: {train_path}")
    if not os.path.exists(val_path):
        raise FileNotFoundError(f"找不到验证数据文件: {val_path}")
    
    # 加载训练和验证数据
    train_df = pd.read_csv(train_path)
    val_df = pd.read_csv(val_path)
    
    # 合并训练和验证数据用于特征提取
    df = pd.concat([train_df, val_df], ignore_index=True)
    
    print(f"✅ 成功加载数据:")
    print(f"   训练数据: {len(train_df)} 行")
    print(f"   验证数据: {len(val_df)} 行")
    print(f"   合并数据: {len(df)} 行")
    print(f"   窗口数量: {df['window_id'].nunique()}")
    print(f"   异常窗口: {df[df['label']==1]['window_id'].nunique()}")
    print(f"   正常窗口: {df[df['label']==0]['window_id'].nunique()}")
    
    return df

def analyze_data_quality(df):
    """分析数据质量"""
    print("\n🔍 数据质量分析...")
    
    # 时间间隔分析
    df['ts'] = pd.to_datetime(df['ts'])
    time_diffs = df.groupby('window_id')['ts'].apply(lambda x: x.diff().median())
    median_interval = time_diffs.median()
    print(f"   中位数采集间隔: {median_interval}")
    
    # 窗口数据点分布
    window_sizes = df.groupby('window_id').size()
    print(f"   窗口大小分布:")
    print(f"     平均: {window_sizes.mean():.1f} 点")
    print(f"     范围: {window_sizes.min()} - {window_sizes.max()} 点")
    
    # 缺失值检查
    missing_info = df.isnull().sum()
    if missing_info.sum() > 0:
        print(f"   ⚠️  发现缺失值:")
        for col, count in missing_info[missing_info > 0].items():
            print(f"     {col}: {count} 个")
    else:
        print(f"   ✅ 无缺失值")
    
    return {
        'median_interval': str(median_interval),
        'window_sizes': {
            'mean': float(window_sizes.mean()),
            'min': int(window_sizes.min()),
            'max': int(window_sizes.max())
        },
        'missing_values': missing_info.to_dict()
    }

def prepare_tsfresh_data(df):
    """准备tsfresh格式的数据"""
    print("\n🔧 准备tsfresh数据格式...")
    
    # 确保时间戳格式正确
    df['ts'] = pd.to_datetime(df['ts'])
    
    # 使用全局配置的核心特征列
    print(f"   配置的核心特征列: {CORE_FEATURES}")
    
    # 检查哪些核心特征列存在于数据中
    available_features = [col for col in CORE_FEATURES if col in df.columns]
    missing_features = [col for col in CORE_FEATURES if col not in df.columns]
    
    print(f"   可用特征列: {len(available_features)} 个")
    if missing_features:
        print(f"   ⚠️  缺失特征列: {missing_features}")
    
    # 数据类型检查和清理
    print("\n🔍 数据类型检查和清理...")
    cleaned_features = []
    
    for col in available_features:
        print(f"   检查列 '{col}':")
        print(f"     原始数据类型: {df[col].dtype}")
        print(f"     唯一值数量: {df[col].nunique()}")
        print(f"     缺失值: {df[col].isnull().sum()}")
        
        # 检查是否为数值类型
        if df[col].dtype in ['object', 'string']:
            print(f"     ❌ 跳过非数值列: {col}")
            continue
            
        # 尝试转换为数值类型
        try:
            # 强制转换为float，处理任何非数值数据
            df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 检查转换后的缺失值
            null_count = df[col].isnull().sum()
            if null_count > 0:
                print(f"     ⚠️  转换后产生 {null_count} 个缺失值")
                # 用列的均值填充缺失值
                df[col] = df[col].fillna(df[col].mean())
                print(f"     ✅ 已用均值填充缺失值")
            
            # 检查是否有无穷大值
            inf_count = np.isinf(df[col]).sum()
            if inf_count > 0:
                print(f"     ⚠️  发现 {inf_count} 个无穷大值")
                df[col] = df[col].replace([np.inf, -np.inf], df[col].median())
                print(f"     ✅ 已用中位数替换无穷大值")
            
            # 验证最终数据类型
            if df[col].dtype in ['int64', 'float64']:
                cleaned_features.append(col)
                print(f"     ✅ 列 '{col}' 清理完成")
            else:
                print(f"     ❌ 列 '{col}' 仍然不是数值类型，跳过")
                
        except Exception as e:
            print(f"     ❌ 列 '{col}' 处理失败: {e}")
            continue
    
    print(f"\n   最终使用特征列: {cleaned_features}")
    
    # 准备tsfresh输入格式（只使用清理后的特征列）
    required_cols = ['window_id', 'ts'] + cleaned_features
    tsfresh_df = df[required_cols].copy()
    
    # 最后检查：确保所有特征列都是数值类型
    for col in cleaned_features:
        if not pd.api.types.is_numeric_dtype(tsfresh_df[col]):
            print(f"     ⚠️  警告: 列 '{col}' 不是数值类型: {tsfresh_df[col].dtype}")
    
    # 按window_id和时间排序，确保时间序列正确
    tsfresh_df = tsfresh_df.sort_values(['window_id', 'ts']).reset_index(drop=True)
    
    # 提取标签
    labels = df.groupby('window_id')['label'].first().reset_index()
    
    print(f"\n   ✅ 数据准备完成:")
    print(f"   tsfresh数据形状: {tsfresh_df.shape}")
    print(f"   有效特征列数: {len(cleaned_features)}")
    print(f"   标签数量: {len(labels)}")
    print(f"   异常窗口: {labels['label'].sum()} 个")
    print(f"   正常窗口: {(labels['label'] == 0).sum()} 个")
    
    return tsfresh_df, labels, cleaned_features

def extract_tsfresh_features(tsfresh_df, use_minimal=True):
    """提取tsfresh特征"""
    print(f"\n⚡ 开始提取tsfresh特征 ({'Minimal' if use_minimal else 'Comprehensive'})...")
    
    # 选择特征参数
    if use_minimal:
        fc_parameters = MinimalFCParameters()
        print("   使用MinimalFCParameters（快速模式）")
    else:
        fc_parameters = ComprehensiveFCParameters()
        print("   使用ComprehensiveFCParameters（全面模式）")
    
    # 提取特征
    try:
        with tqdm(desc="提取特征", unit="window") as pbar:
            X = extract_features(
                tsfresh_df,
                column_id="window_id",
                column_sort="ts", 
                default_fc_parameters=fc_parameters,
                disable_progressbar=True
            )
            pbar.update(len(X))
        
        print(f"✅ 特征提取完成: {X.shape}")
        
        # 处理缺失值
        print("🔄 处理缺失值...")
        X_imputed = impute(X)
        
        missing_before = X.isnull().sum().sum()
        missing_after = X_imputed.isnull().sum().sum()
        print(f"   缺失值: {missing_before} → {missing_after}")
        
        return X_imputed
        
    except Exception as e:
        print(f"❌ 特征提取失败: {e}")
        raise

def feature_selection(X, y, labels):
    """特征选择（可选）"""
    print("\n🎯 进行特征选择...")
    
    try:
        # 准备标签数据，确保索引对齐
        y_series = labels.set_index('window_id')['label']
        y_aligned = y_series.reindex(X.index)
        
        print(f"   原始特征数: {X.shape[1]}")
        
        # tsfresh自动特征选择
        X_selected = select_features(X, y_aligned)
        
        print(f"   选择后特征数: {X_selected.shape[1]}")
        print(f"   特征压缩率: {(1 - X_selected.shape[1]/X.shape[1])*100:.1f}%")
        
        # 显示重要特征
        selected_features = X_selected.columns.tolist()[:10]
        print(f"   前10个特征: {selected_features}")
        
        return X_selected, selected_features
        
    except Exception as e:
        print(f"⚠️  特征选择失败，使用全部特征: {e}")
        return X, X.columns.tolist()

def save_results(X, labels, feature_cols, selected_features, data_quality_info):
    """保存结果"""
    print("\n💾 保存特征数据...")
    
    # 重新加载原始窗口数据，正确获取dataset_type信息
    train_df = pd.read_csv("data/processed_data/train_windows.csv")
    val_df = pd.read_csv("data/processed_data/val_windows.csv")
    
    # 为数据添加正确的dataset_type标记
    train_df['dataset_type'] = 'train'
    val_df['dataset_type'] = 'val'
    
    # 合并数据并创建window_id到dataset_type的映射
    merged_df = pd.concat([train_df, val_df], ignore_index=True)
    window_to_type = merged_df.groupby('window_id')['dataset_type'].first().to_dict()
    
    print(f"   调试信息:")
    print(f"     训练数据窗口数: {len(train_df['window_id'].unique())}")
    print(f"     验证数据窗口数: {len(val_df['window_id'].unique())}")
    print(f"     训练数据异常窗口: {(train_df.groupby('window_id')['label'].first() == 1).sum()}")
    print(f"     验证数据异常窗口: {(val_df.groupby('window_id')['label'].first() == 1).sum()}")
    print(f"     特征矩阵window_id范围: {X.index.min()} - {X.index.max()}")
    
    # 使用正确的映射为特征矩阵分类
    X_with_info = X.copy()
    X_with_info['dataset_type'] = X_with_info.index.map(window_to_type)
    
    # 检查映射结果
    type_counts = X_with_info['dataset_type'].value_counts()
    print(f"     特征矩阵分类结果: {dict(type_counts)}")
    
    # 检查是否有未映射的window_id
    unmapped = X_with_info['dataset_type'].isnull().sum()
    if unmapped > 0:
        print(f"     ⚠️  警告: {unmapped} 个window_id无法映射到dataset_type")
        print(f"     未映射的window_id: {X_with_info[X_with_info['dataset_type'].isnull()].index.tolist()}")
    
    # 分割特征矩阵
    X_train = X_with_info[X_with_info['dataset_type'] == 'train'].drop('dataset_type', axis=1)
    X_val = X_with_info[X_with_info['dataset_type'] == 'val'].drop('dataset_type', axis=1)
    
    # 分割标签 - 使用相同的映射逻辑
    labels_with_type = labels.copy()
    labels_with_type['dataset_type'] = labels_with_type['window_id'].map(window_to_type)
    
    y_train = labels_with_type[labels_with_type['dataset_type'] == 'train'].drop('dataset_type', axis=1)
    y_val = labels_with_type[labels_with_type['dataset_type'] == 'val'].drop('dataset_type', axis=1)
    
    # 验证比例是否正确
    if len(y_train) > 0:
        train_anomaly_rate = y_train['label'].sum() / len(y_train)
        print(f"     训练集异常率: {train_anomaly_rate:.1%}")
    
    if len(y_val) > 0:
        val_anomaly_rate = y_val['label'].sum() / len(y_val)
        print(f"     验证集异常率: {val_anomaly_rate:.1%}")
    
    # 保存训练特征和标签
    train_features_path = "data/processed_data/train_tsfresh_features.csv"
    X_train.to_csv(train_features_path, index=True)
    train_labels_path = "data/processed_data/train_feature_labels.csv"
    y_train.to_csv(train_labels_path, index=False)
    
    # 保存验证特征和标签
    val_features_path = "data/processed_data/val_tsfresh_features.csv"
    X_val.to_csv(val_features_path, index=True)
    val_labels_path = "data/processed_data/val_feature_labels.csv"
    y_val.to_csv(val_labels_path, index=False)
    
    print(f"   训练特征: {train_features_path} {X_train.shape}")
    print(f"   训练标签: {train_labels_path} ({len(y_train)} 个)")
    print(f"   验证特征: {val_features_path} {X_val.shape}")
    print(f"   验证标签: {val_labels_path} ({len(y_val)} 个)")
    
    # 保存完整特征矩阵（用于后续分析）
    full_features_path = "data/processed_data/tsfresh_features.csv"
    X.to_csv(full_features_path, index=True)
    full_labels_path = "data/processed_data/feature_labels.csv"
    labels.to_csv(full_labels_path, index=False)
    
    print(f"   完整特征: {full_features_path} {X.shape}")
    print(f"   完整标签: {full_labels_path} ({len(labels)} 个)")
    
    # 保存元数据 - 确保所有数值都是Python原生类型
    def convert_to_serializable(obj):
        """将numpy类型转换为Python原生类型"""
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, list):
            return [convert_to_serializable(item) for item in obj]
        elif isinstance(obj, dict):
            return {key: convert_to_serializable(value) for key, value in obj.items()}
        else:
            return obj
    
    metadata = {
        'extraction_time': datetime.now().isoformat(),
        'feature_shape': [int(X.shape[0]), int(X.shape[1])],
        'train_shape': [int(X_train.shape[0]), int(X_train.shape[1])],
        'val_shape': [int(X_val.shape[0]), int(X_val.shape[1])],
        'original_feature_columns': feature_cols,
        'selected_features': selected_features[:20] if len(selected_features) > 20 else selected_features,
        'data_quality': convert_to_serializable(data_quality_info),
        'tsfresh_config': {
            'parameters': 'MinimalFCParameters',
            'feature_selection': True,
            'imputation': True
        },
        'data_split': {
            'train_windows': int(len(y_train)),
            'val_windows': int(len(y_val)),
            'train_anomaly_count': int(y_train['label'].sum()) if len(y_train) > 0 else 0,
            'val_anomaly_count': int(y_val['label'].sum()) if len(y_val) > 0 else 0,
            'train_anomaly_rate': float(y_train['label'].mean()) if len(y_train) > 0 else 0.0,
            'val_anomaly_rate': float(y_val['label'].mean()) if len(y_val) > 0 else 0.0
        }
    }
    
    metadata_path = "data/processed_data/feature_metadata.json"
    with open(metadata_path, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, indent=2, ensure_ascii=False)
    print(f"   元数据文件: {metadata_path}")
    
    return train_features_path, val_features_path, metadata_path

def main():
    """主函数"""
    print("🚀 开始tsfresh特征提取流程")
    print("="*60)
    
    try:
        # 1. 创建必要文件夹
        setup_directories()
        
        # 2. 加载数据
        df = load_window_data()
        
        # 3. 数据质量分析
        data_quality_info = analyze_data_quality(df)
        
        # 4. 准备tsfresh数据
        tsfresh_df, labels, feature_cols = prepare_tsfresh_data(df)
        
        # 5. 提取特征
        X = extract_tsfresh_features(tsfresh_df, use_minimal=True)
        
        # 6. 特征选择
        X_selected, selected_features = feature_selection(X, None, labels)
        
        # 7. 保存结果
        train_features_path, val_features_path, metadata_path = save_results(
            X_selected, labels, feature_cols, selected_features, data_quality_info
        )
        
        print("\n" + "="*60)
        print("✅ tsfresh特征提取完成!")
        print(f"📊 最终特征矩阵: {X_selected.shape}")
        print(f"📁 训练特征: {train_features_path}")
        print(f"📁 验证特征: {val_features_path}")
        print(f"📋 元数据文件: {metadata_path}")
        print("\n🎯 接下来可以运行: python 2_train_xgboost_model.py")
        
    except Exception as e:
        print(f"\n❌ 流程执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
