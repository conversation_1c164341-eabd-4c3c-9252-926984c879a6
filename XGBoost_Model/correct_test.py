#!/usr/bin/env python3
"""
严格按照训练流程的测试脚本
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score, roc_auc_score

def load_model_and_config():
    """加载模型和配置"""
    print("📁 加载模型和配置...")
    
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    with open('models/threshold_config.json', 'r') as f:
        threshold_config = json.load(f)
    
    print(f"   模型加载成功")
    print(f"   最佳阈值: {threshold_config['best_threshold']}")
    
    return model, threshold_config

def create_test_windows():
    """创建测试窗口数据（模拟训练时的窗口结构）"""
    print("\n🪟 创建测试窗口数据...")
    
    # 加载原始测试数据
    test_raw = pd.read_csv('data/processed_data/test_raw_data.csv')
    print(f"   原始测试数据: {test_raw.shape}")
    
    # 转换异常标签
    test_raw['anomaly'] = test_raw['is_string_imbalance'].map({
        'True': 1, 'False': 0, True: 1, False: 0
    })
    
    # 按照训练时的方法：每30行为一个窗口
    window_size = 30
    test_raw = test_raw.sort_values('ts').reset_index(drop=True)
    
    # 只保留完整的窗口
    complete_rows = (len(test_raw) // window_size) * window_size
    test_raw = test_raw.iloc[:complete_rows].copy()
    test_raw['window_id'] = test_raw.index // window_size + 1
    
    n_windows = test_raw['window_id'].max()
    print(f"   创建了 {n_windows} 个窗口（每窗口30行）")
    print(f"   使用数据点: {len(test_raw)}")
    
    # 统计异常分布
    anomaly_count = test_raw['anomaly'].sum()
    print(f"   数据点异常率: {anomaly_count}/{len(test_raw)} ({anomaly_count/len(test_raw)*100:.1f}%)")
    
    return test_raw

def extract_features_like_training(test_windows):
    """严格按照训练时的方法提取特征"""
    print("\n🔧 提取特征（严格按照训练流程）...")
    
    # 定义核心特征（与训练脚本完全一致）
    CORE_FEATURES = [
        'cellvdelta',        # 电芯电压差（最重要）
        'averagecellv',      # 平均电芯电压
        'hcellv',           # 最高电芯电压
        'lcellv',           # 最低电芯电压
        'systemvolt',       # 系统电压
        'totalcurrenta',    # 总电流
        'soc',              # SOC
        'htempc',           # 最高温度
        'ltempc'            # 最低温度
        # 注意：移除 balancestatus，因为训练时发现它是非数值的
    ]
    
    # 检查特征可用性
    available_features = [f for f in CORE_FEATURES if f in test_windows.columns]
    print(f"   可用核心特征: {len(available_features)}/{len(CORE_FEATURES)}")
    print(f"   特征列表: {available_features}")
    
    # 按窗口聚合原始特征（与训练时完全一致）
    print("   聚合原始特征...")
    test_agg_features = test_windows.groupby('window_id')[available_features].agg([
        'mean', 'std', 'min', 'max', 'median'
    ]).round(6)
    
    # 展平多级列名（与训练时完全一致）
    test_agg_features.columns = ['_'.join(col).strip() for col in test_agg_features.columns.values]
    test_agg_features.reset_index(drop=True, inplace=True)
    
    print(f"   聚合特征形状: {test_agg_features.shape}")
    
    return test_agg_features, available_features

def create_mock_tsfresh_features(test_agg_features, available_features):
    """创建模拟的TSFresh特征（匹配训练时的特征名称）"""
    print("\n⚙️ 创建TSFresh特征...")
    
    # 由于我们没有完整的TSFresh流程，我们需要创建与训练时相同的特征
    # 从特征重要性文件中获取TSFresh特征名称
    try:
        feature_importance = pd.read_csv('results/feature_importance.csv')
        tsfresh_feature_names = [f for f in feature_importance['feature'] if '__' in f]
        print(f"   从特征重要性文件中找到 {len(tsfresh_feature_names)} 个TSFresh特征")
    except:
        print("   无法加载特征重要性文件，使用默认TSFresh特征")
        tsfresh_feature_names = []
    
    # 创建TSFresh特征DataFrame
    n_windows = len(test_agg_features)
    tsfresh_features = pd.DataFrame()
    
    if tsfresh_feature_names:
        # 使用训练时的特征名称，填充模拟值
        for feature_name in tsfresh_feature_names:
            # 从特征名称中提取基础特征名
            base_feature = feature_name.split('__')[0]
            
            if base_feature in available_features:
                # 根据聚合特征创建对应的TSFresh特征
                if '__maximum' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_max']
                elif '__minimum' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_min']
                elif '__mean' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_mean']
                elif '__standard_deviation' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_std'].fillna(0)
                elif '__median' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_median']
                elif '__variance' in feature_name:
                    tsfresh_features[feature_name] = (test_agg_features[f'{base_feature}_std'] ** 2).fillna(0)
                else:
                    # 对于其他TSFresh特征，使用均值作为默认值
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_mean'].fillna(0)
            else:
                # 如果基础特征不可用，填充0
                tsfresh_features[feature_name] = np.zeros(n_windows)
    
    # 确保所有TSFresh特征都存在（填充缺失的）
    expected_tsfresh_count = 69  # 从训练日志中看到的TSFresh特征数量
    if len(tsfresh_features.columns) < expected_tsfresh_count:
        print(f"   警告: 只创建了 {len(tsfresh_features.columns)} 个TSFresh特征，预期 {expected_tsfresh_count} 个")
    
    print(f"   TSFresh特征形状: {tsfresh_features.shape}")
    
    return tsfresh_features

def create_test_labels(test_windows):
    """创建测试标签"""
    print("\n🏷️ 创建测试标签...")
    
    # 按窗口聚合标签（窗口中有任何异常则标记为异常）
    window_labels = test_windows.groupby('window_id')['anomaly'].max().reset_index()
    y_test = window_labels['anomaly'].values
    
    print(f"   窗口标签分布:")
    print(f"     正常窗口: {np.sum(y_test==0)}")
    print(f"     异常窗口: {np.sum(y_test==1)}")
    print(f"     窗口异常率: {np.mean(y_test)*100:.1f}%")
    
    return y_test

def predict_and_evaluate(model, X_test, y_test, threshold):
    """预测和评估"""
    print(f"\n🔮 模型预测和评估...")

    # 确保特征顺序与训练时一致
    print(f"   输入特征形状: {X_test.shape}")

    # 获取模型期望的特征名称
    expected_features = model.get_booster().feature_names
    print(f"   模型期望特征数: {len(expected_features)}")

    # 重新排序特征以匹配模型期望
    missing_features = []
    available_features = []

    for feature in expected_features:
        if feature in X_test.columns:
            available_features.append(feature)
        else:
            missing_features.append(feature)

    if missing_features:
        print(f"   警告: 缺失 {len(missing_features)} 个特征")
        print(f"   缺失特征示例: {missing_features[:5]}")

        # 为缺失的特征填充0
        for feature in missing_features:
            X_test[feature] = 0

    # 按照模型期望的顺序重新排列特征
    X_test_ordered = X_test[expected_features]
    print(f"   重新排序后特征形状: {X_test_ordered.shape}")

    # 进行预测
    y_pred_proba = model.predict_proba(X_test_ordered)[:, 1]
    y_pred = (y_pred_proba >= threshold).astype(int)
    
    print(f"   预测概率范围: {y_pred_proba.min():.4f} - {y_pred_proba.max():.4f}")
    print(f"   预测异常窗口: {y_pred.sum()}/{len(y_pred)}")
    
    # 计算性能指标
    f1 = f1_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred, zero_division=0)
    recall = recall_score(y_test, y_pred, zero_division=0)
    auc = roc_auc_score(y_test, y_pred_proba)
    
    print(f"\n🎯 测试结果:")
    print(f"   F1-Score: {f1:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   AUC-ROC: {auc:.3f}")
    
    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    print(f"\n   混淆矩阵:")
    print(f"   TN: {cm[0,0]:2d} | FP: {cm[0,1]:2d}")
    print(f"   FN: {cm[1,0]:2d} | TP: {cm[1,1]:2d}")
    
    # 详细分类报告
    print(f"\n   详细分类报告:")
    print(classification_report(y_test, y_pred, target_names=['正常', '异常']))
    
    return {
        'f1_score': float(f1),
        'precision': float(precision),
        'recall': float(recall),
        'auc_roc': float(auc),
        'confusion_matrix': cm.tolist(),
        'test_windows': len(y_test),
        'predicted_anomalies': int(y_pred.sum()),
        'true_anomalies': int(y_test.sum())
    }

def main():
    print("🧪 严格按照训练流程进行模型测试...")
    print("=" * 60)
    
    # 1. 加载模型和配置
    model, threshold_config = load_model_and_config()
    
    # 2. 创建测试窗口
    test_windows = create_test_windows()
    
    # 3. 提取特征（严格按照训练流程）
    test_agg_features, available_features = extract_features_like_training(test_windows)
    
    # 4. 创建TSFresh特征
    test_tsfresh_features = create_mock_tsfresh_features(test_agg_features, available_features)
    
    # 5. 合并特征（与训练时一致）
    X_test = pd.concat([test_agg_features, test_tsfresh_features], axis=1)
    print(f"\n🔗 合并后特征形状: {X_test.shape}")
    
    # 6. 创建标签
    y_test = create_test_labels(test_windows)
    
    # 7. 预测和评估
    results = predict_and_evaluate(model, X_test, y_test, threshold_config['best_threshold'])
    
    # 8. 保存结果
    with open('results/correct_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 结果已保存到: results/correct_test_results.json")
    print("✅ 测试完成！")

if __name__ == "__main__":
    main()
