#!/usr/bin/env python3
"""
严格按照训练流程的测试脚本
"""

import pandas as pd
import numpy as np
import pickle
import json
from sklearn.metrics import classification_report, confusion_matrix, f1_score, precision_score, recall_score, roc_auc_score

def load_model_and_config():
    """加载模型和配置"""
    print("📁 加载模型和配置...")
    
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    with open('models/threshold_config.json', 'r') as f:
        threshold_config = json.load(f)
    
    print(f"   模型加载成功")
    print(f"   最佳阈值: {threshold_config['best_threshold']}")
    
    return model, threshold_config

def create_test_windows():
    """创建测试窗口数据（使用步长1进行实时模拟）"""
    print("\n🪟 创建测试窗口数据...")

    # 加载原始测试数据
    test_raw = pd.read_csv('data/processed_data/test_raw_data.csv')
    print(f"   原始测试数据: {test_raw.shape}")

    # 转换异常标签
    test_raw['anomaly'] = test_raw['is_string_imbalance'].map({
        'True': 1, 'False': 0, True: 1, False: 0
    })

    # 使用步长1创建滑动窗口（模拟实时检测）
    window_size = 30
    step_size = 1  # 步长1，模拟实时检测
    test_raw = test_raw.sort_values('ts').reset_index(drop=True)

    # 创建滑动窗口
    windows_data = []
    window_id = 1

    # 确保有足够的数据创建至少一个完整窗口
    if len(test_raw) < window_size:
        print(f"   警告: 数据不足，需要至少 {window_size} 行数据")
        return pd.DataFrame()

    # 创建滑动窗口
    for start_idx in range(0, len(test_raw) - window_size + 1, step_size):
        end_idx = start_idx + window_size
        window_data = test_raw.iloc[start_idx:end_idx].copy()
        window_data['window_id'] = window_id
        window_data['window_start_idx'] = start_idx
        window_data['window_end_idx'] = end_idx - 1
        windows_data.append(window_data)
        window_id += 1

    # 合并所有窗口数据
    test_windows = pd.concat(windows_data, ignore_index=True)

    n_windows = test_windows['window_id'].max()
    print(f"   创建了 {n_windows} 个滑动窗口（窗口大小30，步长1）")
    print(f"   总数据点: {len(test_windows)} (包含重叠)")
    print(f"   原始数据点: {len(test_raw)}")

    # 统计异常分布
    anomaly_count = test_raw['anomaly'].sum()
    print(f"   原始数据异常率: {anomaly_count}/{len(test_raw)} ({anomaly_count/len(test_raw)*100:.1f}%)")

    return test_windows

def extract_features_like_training(test_windows):
    """严格按照训练时的方法提取特征"""
    print("\n🔧 提取特征（严格按照训练流程）...")
    
    # 定义核心特征（与训练脚本完全一致）
    CORE_FEATURES = [
        'cellvdelta',        # 电芯电压差（最重要）
        'averagecellv',      # 平均电芯电压
        'hcellv',           # 最高电芯电压
        'lcellv',           # 最低电芯电压
        'systemvolt',       # 系统电压
        'totalcurrenta',    # 总电流
        'soc',              # SOC
        'htempc',           # 最高温度
        'ltempc'            # 最低温度
        # 注意：移除 balancestatus，因为训练时发现它是非数值的
    ]
    
    # 检查特征可用性
    available_features = [f for f in CORE_FEATURES if f in test_windows.columns]
    print(f"   可用核心特征: {len(available_features)}/{len(CORE_FEATURES)}")
    print(f"   特征列表: {available_features}")
    
    # 按窗口聚合原始特征（与训练时完全一致）
    print("   聚合原始特征...")
    test_agg_features = test_windows.groupby('window_id')[available_features].agg([
        'mean', 'std', 'min', 'max', 'median'
    ]).round(6)
    
    # 展平多级列名（与训练时完全一致）
    test_agg_features.columns = ['_'.join(col).strip() for col in test_agg_features.columns.values]
    test_agg_features.reset_index(drop=True, inplace=True)
    
    print(f"   聚合特征形状: {test_agg_features.shape}")
    
    return test_agg_features, available_features

def create_mock_tsfresh_features(test_agg_features, available_features):
    """创建模拟的TSFresh特征（匹配训练时的特征名称）"""
    print("\n⚙️ 创建TSFresh特征...")
    
    # 由于我们没有完整的TSFresh流程，我们需要创建与训练时相同的特征
    # 从特征重要性文件中获取TSFresh特征名称
    try:
        feature_importance = pd.read_csv('results/feature_importance.csv')
        tsfresh_feature_names = [f for f in feature_importance['feature'] if '__' in f]
        print(f"   从特征重要性文件中找到 {len(tsfresh_feature_names)} 个TSFresh特征")
    except:
        print("   无法加载特征重要性文件，使用默认TSFresh特征")
        tsfresh_feature_names = []
    
    # 创建TSFresh特征DataFrame
    n_windows = len(test_agg_features)
    tsfresh_features = pd.DataFrame()
    
    if tsfresh_feature_names:
        # 使用训练时的特征名称，填充模拟值
        for feature_name in tsfresh_feature_names:
            # 从特征名称中提取基础特征名
            base_feature = feature_name.split('__')[0]
            
            if base_feature in available_features:
                # 根据聚合特征创建对应的TSFresh特征
                if '__maximum' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_max']
                elif '__minimum' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_min']
                elif '__mean' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_mean']
                elif '__standard_deviation' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_std'].fillna(0)
                elif '__median' in feature_name:
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_median']
                elif '__variance' in feature_name:
                    tsfresh_features[feature_name] = (test_agg_features[f'{base_feature}_std'] ** 2).fillna(0)
                else:
                    # 对于其他TSFresh特征，使用均值作为默认值
                    tsfresh_features[feature_name] = test_agg_features[f'{base_feature}_mean'].fillna(0)
            else:
                # 如果基础特征不可用，填充0
                tsfresh_features[feature_name] = np.zeros(n_windows)
    
    # 确保所有TSFresh特征都存在（填充缺失的）
    expected_tsfresh_count = 69  # 从训练日志中看到的TSFresh特征数量
    if len(tsfresh_features.columns) < expected_tsfresh_count:
        print(f"   警告: 只创建了 {len(tsfresh_features.columns)} 个TSFresh特征，预期 {expected_tsfresh_count} 个")
    
    print(f"   TSFresh特征形状: {tsfresh_features.shape}")
    
    return tsfresh_features

def create_test_labels(test_windows):
    """创建测试标签"""
    print("\n🏷️ 创建测试标签...")
    
    # 按窗口聚合标签（窗口中有任何异常则标记为异常）
    window_labels = test_windows.groupby('window_id')['anomaly'].max().reset_index()
    y_test = window_labels['anomaly'].values
    
    print(f"   窗口标签分布:")
    print(f"     正常窗口: {np.sum(y_test==0)}")
    print(f"     异常窗口: {np.sum(y_test==1)}")
    print(f"     窗口异常率: {np.mean(y_test)*100:.1f}%")
    
    return y_test

def predict_and_evaluate(model, X_test, y_test, threshold):
    """预测和评估"""
    print(f"\n🔮 模型预测和评估...")

    # 确保特征顺序与训练时一致
    print(f"   输入特征形状: {X_test.shape}")

    # 获取模型期望的特征名称
    expected_features = model.get_booster().feature_names
    print(f"   模型期望特征数: {len(expected_features)}")

    # 重新排序特征以匹配模型期望
    missing_features = []
    available_features = []

    for feature in expected_features:
        if feature in X_test.columns:
            available_features.append(feature)
        else:
            missing_features.append(feature)

    if missing_features:
        print(f"   警告: 缺失 {len(missing_features)} 个特征")
        print(f"   缺失特征示例: {missing_features[:5]}")

        # 为缺失的特征填充0
        for feature in missing_features:
            X_test[feature] = 0

    # 按照模型期望的顺序重新排列特征
    X_test_ordered = X_test[expected_features]
    print(f"   重新排序后特征形状: {X_test_ordered.shape}")

    # 进行预测
    y_pred_proba = model.predict_proba(X_test_ordered)[:, 1]
    y_pred = (y_pred_proba >= threshold).astype(int)

    print(f"   预测概率范围: {y_pred_proba.min():.4f} - {y_pred_proba.max():.4f}")
    print(f"   预测异常窗口: {y_pred.sum()}/{len(y_pred)}")

    # 计算性能指标
    f1 = f1_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred, zero_division=0)
    recall = recall_score(y_test, y_pred, zero_division=0)
    auc = roc_auc_score(y_test, y_pred_proba)

    print(f"\n🎯 测试结果:")
    print(f"   F1-Score: {f1:.3f}")
    print(f"   Precision: {precision:.3f}")
    print(f"   Recall: {recall:.3f}")
    print(f"   AUC-ROC: {auc:.3f}")

    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    print(f"\n   混淆矩阵:")
    print(f"   TN: {cm[0,0]:2d} | FP: {cm[0,1]:2d}")
    print(f"   FN: {cm[1,0]:2d} | TP: {cm[1,1]:2d}")

    # 详细分类报告
    print(f"\n   详细分类报告:")
    print(classification_report(y_test, y_pred, target_names=['正常', '异常']))

    return {
        'f1_score': float(f1),
        'precision': float(precision),
        'recall': float(recall),
        'auc_roc': float(auc),
        'confusion_matrix': cm.tolist(),
        'test_windows': len(y_test),
        'predicted_anomalies': int(y_pred.sum()),
        'true_anomalies': int(y_test.sum()),
        'window_predictions': y_pred_proba.tolist(),
        'window_labels': y_test.tolist()
    }

def aggregate_overlapping_predictions(test_windows, window_predictions, window_labels):
    """聚合重叠窗口的预测结果到原始时间点"""
    print(f"\n🔄 聚合重叠窗口预测结果...")

    # 获取原始数据的长度
    original_length = test_windows['window_start_idx'].max() + 30  # 最后一个窗口的结束位置

    # 为每个原始时间点收集所有覆盖它的窗口预测
    point_predictions = {}
    point_labels = {}

    # 遍历每个窗口
    unique_windows = test_windows[['window_id', 'window_start_idx', 'window_end_idx']].drop_duplicates()

    for _, window_info in unique_windows.iterrows():
        window_id = int(window_info['window_id'])
        start_idx = int(window_info['window_start_idx'])
        end_idx = int(window_info['window_end_idx'])

        window_pred = window_predictions[window_id - 1]  # window_id从1开始

        # 获取该窗口的真实标签（窗口内任何异常则为异常）
        window_data = test_windows[test_windows['window_id'] == window_id]
        window_label = window_data['anomaly'].max()

        # 将窗口预测分配给窗口内的每个时间点
        for point_idx in range(start_idx, end_idx + 1):
            if point_idx not in point_predictions:
                point_predictions[point_idx] = []
                point_labels[point_idx] = []

            point_predictions[point_idx].append(window_pred)
            point_labels[point_idx].append(window_label)

    # 聚合每个时间点的预测结果
    aggregated_predictions = []
    aggregated_labels = []

    for point_idx in range(original_length):
        if point_idx in point_predictions:
            # 使用最大值聚合（如果任何窗口预测为异常，则该点为异常）
            max_pred = max(point_predictions[point_idx])
            # 标签使用第一个窗口的标签（它们应该都一样）
            label = point_labels[point_idx][0]

            aggregated_predictions.append(max_pred)
            aggregated_labels.append(label)
        else:
            # 如果某个点没有被任何窗口覆盖（理论上不应该发生）
            aggregated_predictions.append(0.0)
            aggregated_labels.append(0)

    aggregated_predictions = np.array(aggregated_predictions)
    aggregated_labels = np.array(aggregated_labels)

    print(f"   原始时间点数量: {len(aggregated_predictions)}")
    print(f"   聚合后异常点数量: {np.sum(aggregated_labels)}")

    return aggregated_predictions, aggregated_labels

def evaluate_point_level_performance(aggregated_predictions, aggregated_labels, threshold):
    """评估时间点级别的性能"""
    print(f"\n📊 时间点级别性能评估...")

    # 应用阈值
    point_pred_binary = (aggregated_predictions >= threshold).astype(int)

    # 计算性能指标
    f1 = f1_score(aggregated_labels, point_pred_binary)
    precision = precision_score(aggregated_labels, point_pred_binary, zero_division=0)
    recall = recall_score(aggregated_labels, point_pred_binary, zero_division=0)
    auc = roc_auc_score(aggregated_labels, aggregated_predictions)

    print(f"   时间点级别 F1-Score: {f1:.3f}")
    print(f"   时间点级别 Precision: {precision:.3f}")
    print(f"   时间点级别 Recall: {recall:.3f}")
    print(f"   时间点级别 AUC-ROC: {auc:.3f}")

    # 混淆矩阵
    cm = confusion_matrix(aggregated_labels, point_pred_binary)
    print(f"\n   时间点级别混淆矩阵:")
    print(f"   TN: {cm[0,0]:4d} | FP: {cm[0,1]:4d}")
    print(f"   FN: {cm[1,0]:4d} | TP: {cm[1,1]:4d}")

    # 详细分类报告
    print(f"\n   时间点级别详细分类报告:")
    print(classification_report(aggregated_labels, point_pred_binary, target_names=['正常', '异常']))

    return {
        'point_level_f1_score': float(f1),
        'point_level_precision': float(precision),
        'point_level_recall': float(recall),
        'point_level_auc_roc': float(auc),
        'point_level_confusion_matrix': cm.tolist(),
        'total_points': len(aggregated_labels),
        'predicted_anomaly_points': int(point_pred_binary.sum()),
        'true_anomaly_points': int(aggregated_labels.sum())
    }

def main():
    print("🧪 严格按照训练流程进行模型测试（步长1实时模拟）...")
    print("=" * 60)

    # 1. 加载模型和配置
    model, threshold_config = load_model_and_config()

    # 2. 创建测试窗口（步长1）
    test_windows = create_test_windows()

    if test_windows.empty:
        print("❌ 无法创建测试窗口，退出测试")
        return

    # 3. 提取特征（严格按照训练流程）
    test_agg_features, available_features = extract_features_like_training(test_windows)

    # 4. 创建TSFresh特征
    test_tsfresh_features = create_mock_tsfresh_features(test_agg_features, available_features)

    # 5. 合并特征（与训练时一致）
    X_test = pd.concat([test_agg_features, test_tsfresh_features], axis=1)
    print(f"\n🔗 合并后特征形状: {X_test.shape}")

    # 6. 创建标签
    y_test = create_test_labels(test_windows)

    # 7. 窗口级别预测和评估
    print(f"\n{'='*20} 窗口级别评估 {'='*20}")
    window_results = predict_and_evaluate(model, X_test, y_test, threshold_config['best_threshold'])

    # 8. 聚合重叠窗口预测到时间点级别
    aggregated_predictions, aggregated_labels = aggregate_overlapping_predictions(
        test_windows,
        window_results['window_predictions'],
        window_results['window_labels']
    )

    # 9. 时间点级别评估
    print(f"\n{'='*20} 时间点级别评估 {'='*20}")
    point_results = evaluate_point_level_performance(
        aggregated_predictions,
        aggregated_labels,
        threshold_config['best_threshold']
    )

    # 10. 合并结果
    final_results = {
        'window_level': window_results,
        'point_level': point_results,
        'test_config': {
            'window_size': 30,
            'step_size': 1,
            'threshold': threshold_config['best_threshold']
        }
    }

    # 11. 保存结果
    with open('results/correct_test_results_step1.json', 'w') as f:
        json.dump(final_results, f, indent=2)

    print(f"\n💾 结果已保存到: results/correct_test_results_step1.json")
    print("\n📊 测试总结:")
    print(f"   窗口级别 F1-Score: {window_results['f1_score']:.3f}")
    print(f"   时间点级别 F1-Score: {point_results['point_level_f1_score']:.3f}")
    print(f"   窗口级别 Recall: {window_results['recall']:.3f}")
    print(f"   时间点级别 Recall: {point_results['point_level_recall']:.3f}")
    print("✅ 测试完成！")

if __name__ == "__main__":
    main()
