电芯不平衡异常检测模型性能报告
=====================================

训练信息:
- 模型类型: XGBoost 3.0.2
- 训练时间: 2025-06-02 16:58:09
- 特征数量: 152

性能对比:
                基础模型    最终模型    提升
F1-Score        1.000      1.000      +0.000
AUC-ROC         1.000      1.000      +0.000
Precision       1.000      1.000      +0.000
Recall          1.000      1.000      +0.000

超参数调优:
- 测试组合数: 27
- 最佳F1分数: 1.000

最终配置:
- 分类阈值: 0.250
- AUC-ROC: 1.000
- AUC-PR: 1.000

混淆矩阵:
        预测
        正常  异常
实际 正常  27    0
     异常   0   13

使用说明:
1. 使用 models/usage_example.py 进行预测
2. 模型适用于电芯不平衡异常检测
3. 建议定期监控模型性能并重训练