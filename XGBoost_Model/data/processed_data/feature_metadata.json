{"extraction_time": "2025-06-02T16:57:52.540006", "feature_shape": [200, 87], "train_shape": [160, 87], "val_shape": [40, 87], "original_feature_columns": ["cellvdel<PERSON>", "averagecellv", "hcellv", "lcellv", "systemvolt", "totalcurrenta", "soc", "htempc", "ltempc", "tempcdel<PERSON>", "averagecelltempc", "pcspowerset", "pcspower"], "selected_features": ["averagecellv__absolute_maximum", "averagecellv__maximum", "systemvolt__maximum", "systemvolt__absolute_maximum", "lcellv__maximum", "lcellv__absolute_maximum", "cellvdelta__minimum", "averagecellv__median", "systemvolt__median", "cellvdelta__standard_deviation"], "data_quality": {"median_interval": "0 days 00:00:26", "window_sizes": {"mean": 30.0, "min": 30, "max": 30}, "missing_values": {"id": 0, "ts": 0, "softversion": 0, "stringid": 0, "macid": 0, "heart_beat": 0, "sysstatus": 0, "lvol": 0, "hvol": 0, "systemvolt": 0, "totalcurrenta": 0, "soc": 0, "soh": 0, "ccl_a": 0, "dcl_a": 0, "hcellv": 0, "lcellv": 0, "hcellstring": 0, "lcellstring": 0, "hcellmodule": 0, "lcellmodule": 0, "hcell": 0, "lcell": 0, "htempc": 0, "ltempc": 0, "htempstring": 0, "ltempstring": 0, "htempmodule": 0, "ltempmodule": 0, "averagecellv": 0, "averagecelltempc": 0, "interval_mins": 0, "cellvdelta": 0, "tempcdelta": 0, "meterdemand": 0, "egtogrid": 0, "egfromgrid": 0, "egpcschg": 0, "egpcsdchg": 0, "pcspowerset": 0, "pcspower": 0, "balancestatus": 0, "realload": 0, "mins": 0, "sysid": 0, "localip": 6000, "evt1": 0, "evt2": 0, "pcsevt1": 0, "atsst": 0, "ambienttemp": 0, "humidity": 0, "is_string_imbalance": 0, "window_id": 0, "label": 0, "dataset_type": 0}}, "tsfresh_config": {"parameters": "MinimalFCParameters", "feature_selection": true, "imputation": true}, "data_split": {"train_windows": 160, "val_windows": 40, "train_anomaly_count": 37, "val_anomaly_count": 13, "train_anomaly_rate": 0.23125, "val_anomaly_rate": 0.325}}