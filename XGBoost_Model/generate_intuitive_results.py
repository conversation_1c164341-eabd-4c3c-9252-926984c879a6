#!/usr/bin/env python3
"""
生成直观的测试结果对比
在原始测试数据上添加预测标签，方便直观查看准确度
"""

import pandas as pd
import numpy as np
import pickle
import json
from datetime import datetime

def load_model_and_config():
    """加载模型和配置"""
    print("📁 加载模型和配置...")
    
    with open('models/xgboost_model.pkl', 'rb') as f:
        model = pickle.load(f)
    
    with open('models/threshold_config.json', 'r') as f:
        threshold_config = json.load(f)
    
    # 使用F1优化后的最佳阈值
    best_threshold = 0.46  # 从improve_f1_test.py的结果
    
    print(f"   模型加载成功")
    print(f"   使用F1优化阈值: {best_threshold}")
    
    return model, best_threshold

def create_sliding_windows_with_predictions():
    """创建滑动窗口并进行预测"""
    print("\n🪟 创建滑动窗口数据...")
    
    # 加载原始测试数据
    test_raw = pd.read_csv('data/processed_data/test_raw_data.csv')
    print(f"   原始测试数据: {test_raw.shape}")
    
    # 转换异常标签
    test_raw['anomaly'] = test_raw['is_string_imbalance'].map({
        'True': 1, 'False': 0, True: 1, False: 0
    })
    
    # 使用步长1创建滑动窗口
    window_size = 30
    step_size = 1
    test_raw = test_raw.sort_values('ts').reset_index(drop=True)
    
    # 定义核心特征
    CORE_FEATURES = [
        'cellvdelta', 'averagecellv', 'hcellv', 'lcellv', 
        'systemvolt', 'totalcurrenta', 'soc', 'htempc', 'ltempc',
        'tempcdelta', 'averagecelltempc', 'pcspowerset', 'pcspower'
    ]
    
    # 检查特征可用性
    available_features = [f for f in CORE_FEATURES if f in test_raw.columns]
    print(f"   可用核心特征: {len(available_features)}/{len(CORE_FEATURES)}")
    
    # 创建滑动窗口
    windows_data = []
    window_id = 1
    
    if len(test_raw) < window_size:
        print(f"   警告: 数据不足，需要至少 {window_size} 行数据")
        return pd.DataFrame()
    
    for start_idx in range(0, len(test_raw) - window_size + 1, step_size):
        end_idx = start_idx + window_size
        window_data = test_raw.iloc[start_idx:end_idx].copy()
        window_data['window_id'] = window_id
        window_data['window_start_idx'] = start_idx
        window_data['window_end_idx'] = end_idx - 1
        windows_data.append(window_data)
        window_id += 1
    
    test_windows = pd.concat(windows_data, ignore_index=True)
    n_windows = test_windows['window_id'].max()
    print(f"   创建了 {n_windows} 个滑动窗口")
    
    return test_windows, available_features

def extract_features_and_predict(test_windows, available_features, model, threshold):
    """提取特征并进行预测"""
    print("\n🔧 提取特征并预测...")
    
    # 按窗口聚合原始特征
    test_agg_features = test_windows.groupby('window_id')[available_features].agg([
        'mean', 'std', 'min', 'max', 'median'
    ]).round(6)
    
    test_agg_features.columns = ['_'.join(col).strip() for col in test_agg_features.columns.values]
    test_agg_features.reset_index(drop=True, inplace=True)
    
    # 创建TSFresh特征
    try:
        feature_importance = pd.read_csv('results/feature_importance.csv')
        tsfresh_feature_names = [f for f in feature_importance['feature'] if '__' in f]
    except:
        tsfresh_feature_names = []
    
    n_windows = len(test_agg_features)
    tsfresh_features = pd.DataFrame()
    
    if tsfresh_feature_names:
        for feature_name in tsfresh_feature_names:
            if '__' in feature_name:
                base_feature = feature_name.split('__')[0]
                base_mean_col = f'{base_feature}_mean'
                base_max_col = f'{base_feature}_max'
                base_min_col = f'{base_feature}_min'
                base_std_col = f'{base_feature}_std'
                base_median_col = f'{base_feature}_median'
                
                if base_mean_col in test_agg_features.columns:
                    if '__maximum' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_max_col]
                    elif '__minimum' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_min_col]
                    elif '__mean' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_mean_col]
                    elif '__standard_deviation' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_std_col].fillna(0)
                    elif '__median' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_median_col]
                    elif '__variance' in feature_name:
                        tsfresh_features[feature_name] = (test_agg_features[base_std_col] ** 2).fillna(0)
                    elif '__absolute_maximum' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_max_col].abs()
                    elif '__root_mean_square' in feature_name:
                        tsfresh_features[feature_name] = np.sqrt((test_agg_features[base_mean_col] ** 2).fillna(0))
                    elif '__sum_values' in feature_name:
                        tsfresh_features[feature_name] = test_agg_features[base_mean_col] * 30
                    else:
                        tsfresh_features[feature_name] = test_agg_features[base_mean_col].fillna(0)
                else:
                    tsfresh_features[feature_name] = np.zeros(n_windows)
    
    tsfresh_features = tsfresh_features.fillna(0)
    
    # 合并特征
    X_test = pd.concat([test_agg_features, tsfresh_features], axis=1)
    print(f"   合并后特征形状: {X_test.shape}")
    
    # 创建窗口标签
    window_labels = test_windows.groupby('window_id')['anomaly'].max().reset_index()
    y_test = window_labels['anomaly'].values
    
    # 获取模型期望的特征并重新排序
    expected_features = model.get_booster().feature_names
    missing_features = [f for f in expected_features if f not in X_test.columns]
    
    if missing_features:
        print(f"   警告: 缺失 {len(missing_features)} 个特征，用0填充")
        for feature in missing_features:
            X_test[feature] = 0
    
    X_test_ordered = X_test[expected_features]
    
    # 进行预测
    y_pred_proba = model.predict_proba(X_test_ordered)[:, 1]
    y_pred = (y_pred_proba >= threshold).astype(int)
    
    print(f"   预测完成: {len(y_pred)} 个窗口")
    print(f"   预测异常窗口: {y_pred.sum()}/{len(y_pred)}")
    
    return y_pred_proba, y_pred, y_test

def aggregate_to_point_level(test_windows, window_predictions, window_labels):
    """将窗口级预测聚合到时间点级别"""
    print("\n🔄 聚合到时间点级别...")
    
    # 获取原始数据长度
    original_length = test_windows['window_start_idx'].max() + 30
    
    # 为每个时间点收集预测结果
    point_predictions = {}
    point_labels = {}
    
    unique_windows = test_windows[['window_id', 'window_start_idx', 'window_end_idx']].drop_duplicates()
    
    for _, window_info in unique_windows.iterrows():
        window_id = int(window_info['window_id'])
        start_idx = int(window_info['window_start_idx'])
        end_idx = int(window_info['window_end_idx'])
        
        window_pred = window_predictions[window_id - 1]
        window_label = window_labels[window_id - 1]
        
        for point_idx in range(start_idx, end_idx + 1):
            if point_idx not in point_predictions:
                point_predictions[point_idx] = []
                point_labels[point_idx] = []
            
            point_predictions[point_idx].append(window_pred)
            point_labels[point_idx].append(window_label)
    
    # 聚合结果（使用最大值）
    aggregated_predictions = []
    aggregated_labels = []
    
    for point_idx in range(original_length):
        if point_idx in point_predictions:
            max_pred = max(point_predictions[point_idx])
            label = point_labels[point_idx][0]
            aggregated_predictions.append(max_pred)
            aggregated_labels.append(label)
        else:
            aggregated_predictions.append(0.0)
            aggregated_labels.append(0)
    
    return np.array(aggregated_predictions), np.array(aggregated_labels)

def create_intuitive_results(test_raw, point_predictions, point_labels, threshold):
    """创建直观的结果对比"""
    print("\n📊 创建直观结果对比...")
    
    # 确保长度匹配
    min_length = min(len(test_raw), len(point_predictions), len(point_labels))
    
    # 创建结果DataFrame
    results_df = test_raw.iloc[:min_length].copy()
    
    # 添加预测结果
    results_df['predicted_probability'] = point_predictions[:min_length]
    results_df['predicted_label'] = (point_predictions[:min_length] >= threshold).astype(int)
    results_df['true_label'] = point_labels[:min_length]
    
    # 添加预测正确性标记
    results_df['prediction_correct'] = (results_df['predicted_label'] == results_df['true_label'])
    results_df['prediction_status'] = results_df.apply(lambda row: 
        'TP' if row['true_label'] == 1 and row['predicted_label'] == 1 else
        'TN' if row['true_label'] == 0 and row['predicted_label'] == 0 else
        'FP' if row['true_label'] == 0 and row['predicted_label'] == 1 else
        'FN', axis=1)
    
    # 重新排列列的顺序，把重要信息放在前面
    important_cols = [
        'ts', 'true_label', 'predicted_label', 'predicted_probability', 
        'prediction_correct', 'prediction_status', 'is_string_imbalance'
    ]
    
    other_cols = [col for col in results_df.columns if col not in important_cols]
    results_df = results_df[important_cols + other_cols]
    
    return results_df

def generate_summary_stats(results_df):
    """生成汇总统计"""
    print("\n📈 生成汇总统计...")
    
    total_points = len(results_df)
    correct_predictions = results_df['prediction_correct'].sum()
    accuracy = correct_predictions / total_points
    
    # 混淆矩阵统计
    tp = len(results_df[(results_df['true_label'] == 1) & (results_df['predicted_label'] == 1)])
    tn = len(results_df[(results_df['true_label'] == 0) & (results_df['predicted_label'] == 0)])
    fp = len(results_df[(results_df['true_label'] == 0) & (results_df['predicted_label'] == 1)])
    fn = len(results_df[(results_df['true_label'] == 1) & (results_df['predicted_label'] == 0)])
    
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0
    recall = tp / (tp + fn) if (tp + fn) > 0 else 0
    f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
    
    summary = {
        'total_data_points': int(total_points),
        'correct_predictions': int(correct_predictions),
        'accuracy': float(accuracy),
        'true_positives': int(tp),
        'true_negatives': int(tn),
        'false_positives': int(fp),
        'false_negatives': int(fn),
        'precision': float(precision),
        'recall': float(recall),
        'f1_score': float(f1),
        'anomaly_rate_true': float(results_df['true_label'].mean()),
        'anomaly_rate_predicted': float(results_df['predicted_label'].mean())
    }
    
    return summary

def main():
    """主函数"""
    print("🎯 生成直观的测试结果对比...")
    print("=" * 60)
    
    # 1. 加载模型
    model, threshold = load_model_and_config()
    
    # 2. 创建滑动窗口
    test_windows, available_features = create_sliding_windows_with_predictions()
    if test_windows.empty:
        print("❌ 无法创建测试窗口")
        return
    
    # 3. 提取特征并预测
    window_pred_proba, window_pred, window_labels = extract_features_and_predict(
        test_windows, available_features, model, threshold
    )
    
    # 4. 聚合到时间点级别
    point_predictions, point_labels = aggregate_to_point_level(
        test_windows, window_pred_proba, window_labels
    )
    
    # 5. 加载原始测试数据
    test_raw = pd.read_csv('data/processed_data/test_raw_data.csv')
    test_raw = test_raw.sort_values('ts').reset_index(drop=True)
    
    # 6. 创建直观结果
    results_df = create_intuitive_results(test_raw, point_predictions, point_labels, threshold)
    
    # 7. 生成汇总统计
    summary = generate_summary_stats(results_df)
    
    # 8. 保存结果
    results_df.to_csv('results/intuitive_test_results.csv', index=False)
    
    with open('results/intuitive_test_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    # 9. 显示结果摘要
    print(f"\n🎯 直观测试结果摘要:")
    print(f"   总数据点: {summary['total_data_points']:,}")
    print(f"   预测正确: {summary['correct_predictions']:,}")
    print(f"   准确率: {summary['accuracy']:.1%}")
    print(f"   F1-Score: {summary['f1_score']:.3f}")
    print(f"   Precision: {summary['precision']:.3f}")
    print(f"   Recall: {summary['recall']:.3f}")
    
    print(f"\n📊 混淆矩阵:")
    print(f"   TP: {summary['true_positives']:4d} | FP: {summary['false_positives']:4d}")
    print(f"   FN: {summary['false_negatives']:4d} | TN: {summary['true_negatives']:4d}")
    
    print(f"\n💾 结果文件:")
    print(f"   详细结果: results/intuitive_test_results.csv")
    print(f"   汇总统计: results/intuitive_test_summary.json")
    
    # 10. 显示部分结果示例
    print(f"\n📋 结果示例 (前10行):")
    display_cols = ['ts', 'true_label', 'predicted_label', 'predicted_probability', 
                   'prediction_correct', 'prediction_status']
    print(results_df[display_cols].head(10).to_string(index=False))
    
    print(f"\n✅ 直观测试结果生成完成！")

if __name__ == "__main__":
    main()
